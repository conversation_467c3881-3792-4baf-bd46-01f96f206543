import { ConversationService } from '../../src/core/ConversationService';
import { MoodService } from '../../src/core/MoodService';
import { CharacterMoods } from '../../src/models/types';

describe('Mood System Integration', () => {
  it('should assign random moods when creating a conversation', async () => {
    const conversation = await ConversationService.createConversation();

    expect(conversation.character_moods).toBeDefined();

    // Parse the moods
    const moods = MoodService.parseCharacterMoods(conversation.character_moods);
    expect(moods).not.toBeNull();

    if (moods) {
      // Verify all characters have moods assigned
      expect(moods.Fora).toBeDefined();
      expect(moods.Jan).toBeDefined();
      expect(moods.Lou).toBeDefined();

      // Verify moods are valid
      const validForaMoods = ['enthusiastic', 'supportive', 'energetic', 'thoughtful', 'determined', 'collaborative'];
      const validJanMoods = ['analytical', 'focused', 'pragmatic', 'direct', 'methodical', 'decisive'];
      const validLouMoods = ['contemplative', 'intuitive', 'empathetic', 'observant', 'reflective', 'grounded'];

      expect(validForaMoods).toContain(moods.Fora);
      expect(validJanMoods).toContain(moods.Jan);
      expect(validLouMoods).toContain(moods.Lou);
    }
  });

  it('should format moods correctly for prompts', () => {
    const testMoods: CharacterMoods = {
      Fora: 'enthusiastic',
      Jan: 'analytical', 
      Lou: 'contemplative'
    };

    const formatted = MoodService.formatMoodsForPrompt(testMoods);
    
    expect(formatted).toContain('Character moods for this conversation:');
    expect(formatted).toContain('Fora is feeling enthusiastic');
    expect(formatted).toContain('Jan is feeling analytical');
    expect(formatted).toContain('Lou is feeling contemplative');
  });

  it('should format individual character moods correctly', () => {
    const testMoods: CharacterMoods = {
      Fora: 'supportive',
      Jan: 'focused',
      Lou: 'empathetic'
    };

    const foraFormatted = MoodService.formatCharacterMoodForPrompt('Fora', testMoods);
    const janFormatted = MoodService.formatCharacterMoodForPrompt('Jan', testMoods);
    const louFormatted = MoodService.formatCharacterMoodForPrompt('Lou', testMoods);

    expect(foraFormatted).toBe("You're in a supportive mood today.");
    expect(janFormatted).toBe("You're in a focused mood today.");
    expect(louFormatted).toBe("You're in a empathetic mood today.");
  });

  it('should generate different moods across multiple conversations', async () => {
    const conversations = await Promise.all([
      ConversationService.createConversation(),
      ConversationService.createConversation(),
      ConversationService.createConversation()
    ]);

    const moodSets = conversations.map(conv => 
      MoodService.parseCharacterMoods(conv.character_moods)
    );

    // At least one character should have different moods across the conversations
    const allSame = moodSets.every((moods, index) => {
      if (index === 0 || !moods || !moodSets[0]) return true;
      return (
        moods.Fora === moodSets[0].Fora &&
        moods.Jan === moodSets[0].Jan &&
        moods.Lou === moodSets[0].Lou
      );
    });

    expect(allSame).toBe(false);
  });
});
