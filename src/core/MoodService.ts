import { CharacterMoods, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '../models/types';

export class MoodService {
  // Character mood definitions
  private static readonly FORA_MOODS: ForaMood[] = [
    'enthusiastic',
    'supportive', 
    'energetic',
    'thoughtful',
    'determined',
    'collaborative'
  ];

  private static readonly JAN_MOODS: JanMood[] = [
    'analytical',
    'focused',
    'pragmatic',
    'direct',
    'methodical',
    'decisive'
  ];

  private static readonly LOU_MOODS: LouMood[] = [
    'contemplative',
    'intuitive',
    'empathetic',
    'observant',
    'reflective',
    'grounded'
  ];

  /**
   * Randomly selects a mood for each character
   */
  static generateRandomMoods(): CharacterMoods {
    return {
      Fora: MoodService.getRandomMood(MoodService.FORA_MOODS),
      Jan: MoodService.getRandomMood(MoodService.JAN_MOODS),
      Lou: MoodService.getRandomMood(MoodService.LOU_MOODS)
    };
  }

  /**
   * Gets a random mood from the provided array
   */
  private static getRandomMood<T>(moods: T[]): T {
    const randomIndex = Math.floor(Math.random() * moods.length);
    return moods[randomIndex];
  }

  /**
   * Formats mood information for prompt injection
   */
  static formatMoodsForPrompt(moods: CharacterMoods): string {
    return `Character moods for this conversation:
- Fora is feeling ${moods.Fora}
- Jan is feeling ${moods.Jan}  
- Lou is feeling ${moods.Lou}`;
  }

  /**
   * Formats individual character mood for character-specific prompts
   */
  static formatCharacterMoodForPrompt(character: string, moods: CharacterMoods): string {
    const mood = moods[character as keyof CharacterMoods];
    if (!mood) {
      return '';
    }
    return `You're in a ${mood} mood today.`;
  }

  /**
   * Parses character moods from database (handles both JSON object and string)
   */
  static parseCharacterMoods(moodsData: CharacterMoods | string | null | undefined): CharacterMoods | null {
    if (!moodsData) {
      return null;
    }

    if (typeof moodsData === 'string') {
      try {
        return JSON.parse(moodsData) as CharacterMoods;
      } catch (error) {
        console.error('Failed to parse character moods JSON:', error);
        return null;
      }
    }

    return moodsData as CharacterMoods;
  }
}
