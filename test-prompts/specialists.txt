Des: The Conflict De-escalator
Des is the go-to for navigating difficult conversations and interpersonal friction. These scenarios involve direct or passive-aggressive conflict.

Scenario #2: Coworker taking credit for your work.

bruh

so my coworker just presented my idea as their own in a meeting

im so heated rn

like, i have the receipts. the literal docs.

how do i bring this up without starting drama?

i dont wanna be that person but that's my work

this is not the vibe

Scenario #9: Navigating a messy team group chat.

the team group chat is popping off for all the wrong reasons

people are getting passive aggressive

someone just dropped a "per my last email"

i'm just trying to stay in my lane

should i mute? leave? say something?

this is giving high school

Scenario #11: A senior coworker is being condescending.

there's this one senior person on my team

and they always talk to me like i'm a child

they'll be like "oh sweetie, that's not how we do things here"

it's so patronizing

how do i get them to respect me?

i'm not a baby, i have a degree

Bon: The Boundary Setter
Bon is the expert for when users need to protect their time, energy, and focus. These scenarios are all about saying "no" and setting clear boundaries.

Scenario #4: Saying "no" to taking on more work.

help my plate is already so full

and my coworker just asked me to help with their project

i wanna be a team player but im drowning

how do i say no without sounding like a hater?

i dont want them to think im not pulling my weight

my work-life balance is a joke rn

<PERSON>enario #6: A coworker is oversharing their personal life.

ok so my deskmate is... a lot

they tell me EVERYTHING. like, TMI

their drama with their situationship, their weird rash, everything

i need to focus but they just keep talking

how do i get them to stop without being rude?

send help, i'm being held hostage by small talk

Scenario #8: A coworker who sends late-night emails.

my coworker has no chill

they keep sending work emails at like 11pm

it's stressing me out

am i supposed to answer them?

i wanna have a life outside of work yknow?

how do i set boundaries without making it weird?

Nat: The Expert Negotiator
Nat's skills are for when a user needs to advocate for themselves, their work, or their career. This scenario involves negotiating for recognition and ownership of work.

Scenario #2: Coworker taking credit for your work.
(Note: This scenario could trigger Des for the conflict aspect or Nat for the negotiation aspect. A sophisticated bot might offer the user a choice.)

bruh

so my coworker just presented my idea as their own in a meeting

im so heated rn

like, i have the receipts. the literal docs.

how do i bring this up without starting drama?

i dont wanna be that person but that's my work

this is not the vibe

Pri: The Presentation Wizard
Since none of the original scenarios are about preparing for a presentation, here is a new script to test the referral to Pri.

New Scenario: Getting help with a presentation.

ok so i have this big presentation coming up

im kinda freaking out

how do i make sure it doesnt suck?

i need to convince the higher-ups on this project

give me the tea on public speaking

help me not sound like an idiot

Irv: The Interview Prep Coach
The provided scenarios are for people already in the workplace. Here is a new script to test a referral to Irv for a user who is in the job-seeking process.

New Scenario: Preparing for a job interview.

omg i just landed an interview for my dream job

the anxiety is real

they said there will be behavioral questions

what does that even mean?

i need to practice

can we do a mock interview or something?

Ren: The Resume Perfecter
Similar to Irv, Ren's specialty is for job seekers. Here is a new script to test a referral to Ren.

New Scenario: Getting resume feedback.

so i'm thinking of looking for a new job

my resume is so cringe tho

it's from like, college

can you look at it?

how do i make it sound like i've actually accomplished things?

i'll send it to you
